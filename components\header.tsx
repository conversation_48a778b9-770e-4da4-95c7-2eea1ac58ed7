"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Menu, X, Spade, Crown } from "lucide-react"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const pathname = usePathname()

  const navigation = [
    { name: "Hem", href: "/" },
    { name: "Artik<PERSON>", href: "/artiklar" },
    { name: "Om Mig", href: "/om" },
    { name: "Kontakt", href: "/kontakt" },
  ]

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? "bg-slate-950/95 backdrop-blur-xl border-b border-amber-500/20 shadow-2xl" : "bg-transparent"
      }`}
    >
      <nav className="container mx-auto px-4">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 group relative">
            <div className="relative">
              {/* Glow effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-amber-400 to-amber-600 rounded-xl blur-lg opacity-30 group-hover:opacity-50 transition-opacity"></div>

              {/* Main logo container */}
              <div className="relative bg-gradient-to-br from-amber-400 via-amber-500 to-amber-600 p-3 rounded-xl group-hover:scale-105 transition-all duration-300 shadow-xl">
                <Spade className="h-7 w-7 text-slate-900" />
              </div>

              {/* Crown indicator */}
              <div className="absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-amber-500 p-1 rounded-full shadow-lg">
                <Crown className="h-3 w-3 text-slate-900" />
              </div>

              {/* Pulse indicator */}
              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-emerald-400 rounded-full animate-pulse shadow-lg"></div>
            </div>

            <div className="hidden sm:block">
              <div className="font-crimson text-2xl font-bold bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-600 bg-clip-text text-transparent">
                Pappa Haj
              </div>
              <div className="text-xs text-amber-400/80 font-medium tracking-wider uppercase">Poker Expert</div>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            {navigation.map((item) => {
              const isActive = pathname === item.href || (item.href === "/artiklar" && pathname.startsWith("/artiklar"))

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`relative px-6 py-3 font-medium transition-all duration-300 group ${
                    isActive ? "text-amber-400" : "text-slate-300 hover:text-amber-400"
                  }`}
                >
                  {/* Background hover effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 to-emerald-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity"></div>

                  {/* Text */}
                  <span className="relative z-10">{item.name}</span>

                  {/* Active indicator */}
                  {isActive && (
                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-gradient-to-r from-amber-400 to-amber-600 rounded-full"></div>
                  )}

                  {/* Hover underline */}
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-amber-400 to-amber-600 rounded-full group-hover:w-8 transition-all duration-300"></div>
                </Link>
              )
            })}
          </div>

          {/* CTA Button - Desktop */}
          <div className="hidden lg:block">
            <Button className="relative overflow-hidden bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-300 group shadow-lg hover:shadow-xl">
              <span className="relative z-10">Börja Lära</span>
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-700 to-emerald-800 opacity-0 group-hover:opacity-100 transition-opacity"></div>
            </Button>
          </div>

          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden relative p-3 text-slate-300 hover:text-amber-400 hover:bg-amber-400/10 rounded-lg transition-all duration-300"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <div className="relative">
              {isMenuOpen ? (
                <X className="h-6 w-6 transition-transform duration-300 rotate-90" />
              ) : (
                <Menu className="h-6 w-6 transition-transform duration-300" />
              )}
            </div>
          </Button>
        </div>

        {/* Mobile Navigation */}
        <div
          className={`lg:hidden overflow-hidden transition-all duration-300 ${
            isMenuOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
          }`}
        >
          <div className="py-4 border-t border-slate-800/50">
            <div className="flex flex-col space-y-2">
              {navigation.map((item) => {
                const isActive =
                  pathname === item.href || (item.href === "/artiklar" && pathname.startsWith("/artiklar"))

                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`relative px-4 py-3 font-medium rounded-lg transition-all duration-300 group ${
                      isActive
                        ? "text-amber-400 bg-amber-400/10"
                        : "text-slate-300 hover:text-amber-400 hover:bg-amber-400/5"
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <div className="flex items-center space-x-3">
                      {isActive && (
                        <div className="w-1 h-6 bg-gradient-to-b from-amber-400 to-amber-600 rounded-full"></div>
                      )}
                      <span>{item.name}</span>
                    </div>
                  </Link>
                )
              })}

              {/* Mobile CTA */}
              <div className="pt-4 px-4">
                <Button className="w-full bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white font-semibold py-3 rounded-lg transition-all duration-300 shadow-lg">
                  Börja Lära
                </Button>
              </div>
            </div>
          </div>
        </div>
      </nav>
    </header>
  )
}
