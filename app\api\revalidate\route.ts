import { type NextRequest, NextResponse } from "next/server"
import { revalidatePath } from "next/cache"

// This API route is used to revalidate the blog pages when new content is added
export async function POST(request: NextRequest) {
  try {
    // You could add authentication here to ensure only authorized requests can revalidate
    const body = await request.json()

    // Check if the request has a secret that matches your environment variable
    const secret = request.headers.get("x-revalidate-secret")
    if (secret !== process.env.REVALIDATE_SECRET) {
      return NextResponse.json({ message: "Invalid secret" }, { status: 401 })
    }

    // Revalidate the specific paths
    if (body.path) {
      revalidatePath(body.path)
      return NextResponse.json({ revalidated: true, path: body.path })
    }

    // Revalidate all blog pages by default
    revalidatePath("/blogg")
    revalidatePath("/artiklar")
    revalidatePath("/")

    // Also revalidate sitemap when content changes
    revalidatePath("/sitemap.xml")

    return NextResponse.json({
      revalidated: true,
      message: "Blog pages and sitemap revalidated",
      paths: ["/blogg", "/artiklar", "/", "/sitemap.xml"]
    })
  } catch (error) {
    return NextResponse.json({ message: "Error revalidating", error }, { status: 500 })
  }
}
