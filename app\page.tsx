import type { <PERSON>ada<PERSON> } from "next"
import Image from "next/image"
import Link from "next/link"
import { CalendarD<PERSON>, Clock, ArrowRight } from "lucide-react"
import { getAllPosts } from "@/lib/blog"

export const metadata: Metadata = {
  title: "Hem",
  description:
    "Välkommen till Pappa Haj - din ultimata guide till pokervärlden. Läs de senaste artiklarna om pokerstrategi, turneringar och tips för att förbättra ditt spel.",
  keywords: ["poker hem", "pokerblogg", "pokerstrategi", "pokerartiklar", "pappa haj hem"],
  openGraph: {
    title: "Pappa Haj - Pokerblogg & Strategier",
    description: "Välkommen till Pappa Haj - din ultimata guide till pokervärlden.",
    url: "https://pappahaj.se",
  },
}

export default async function HomePage() {
  // Get the latest 6 blog posts
  const blogPosts = await getAllPosts()
  const featuredPosts = blogPosts.slice(0, 6)

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative poker-gradient py-20 lg:py-32 overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-10 text-6xl suit-spade"></div>
          <div className="absolute top-40 right-20 text-6xl suit-heart"></div>
          <div className="absolute bottom-40 left-20 text-6xl suit-diamond"></div>
          <div className="absolute bottom-20 right-10 text-6xl suit-club"></div>
        </div>

        <div className="relative container mx-auto px-4">
          <div className="max-w-5xl mx-auto text-center">
            <div className="mb-8">
              <div className="inline-flex items-center px-4 py-2 bg-emerald-900/30 border border-emerald-500/30 rounded-full mb-6">
                <span className="text-emerald-400 text-sm font-medium">Välkommen till pokervärldens elit</span>
              </div>
            </div>

            <h1 className="font-crimson text-6xl lg:text-8xl font-bold mb-6">
              <span className="bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-600 bg-clip-text text-transparent">
                Pappa Haj
              </span>
            </h1>

            <p className="text-xl lg:text-2xl text-slate-300 mb-8 leading-relaxed max-w-3xl mx-auto font-light">
              Mästra pokerkonsten med expertstrategier, djupgående analyser och beprövade tekniker som tar ditt spel
              från amatör till professionell nivå.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Link
                href="/artiklar"
                className="group relative px-8 py-4 bg-gradient-to-r from-amber-500 to-amber-600 text-slate-900 font-semibold rounded-lg overflow-hidden transition-all duration-300 hover:scale-105"
              >
                <span className="relative z-10 flex items-center justify-center">
                  Utforska Artiklar
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-amber-600 to-amber-700 opacity-0 group-hover:opacity-100 transition-opacity"></div>
              </Link>

              <Link
                href="/om"
                className="px-8 py-4 border-2 border-amber-500/50 text-amber-400 font-semibold rounded-lg hover:bg-amber-500/10 transition-all duration-300"
              >
                Min Pokerresa
              </Link>
            </div>

            {/* Hero Background Image */}
            <div className="relative max-w-4xl mx-auto">
              <Image
                src="/images/welcome-hero.jpg"
                alt="Poker Hero Background"
                width={800}
                height={400}
                className="w-full h-64 md:h-80 object-cover rounded-xl opacity-30"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-slate-900/80 via-slate-900/40 to-transparent rounded-xl"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Articles Section */}
      <section className="py-16 lg:py-24 bg-gradient-to-b from-slate-900 to-slate-950">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-emerald-900/20 border border-emerald-500/20 rounded-full mb-6">
              <span className="text-emerald-400 text-sm font-medium">Senaste Insikterna</span>
            </div>
            <h2 className="font-crimson text-4xl lg:text-5xl font-bold mb-6 text-white">Expertartiklar & Analyser</h2>
            <p className="text-slate-400 text-lg max-w-3xl mx-auto leading-relaxed">
              Djupdyk i avancerade pokerstrategier, psykologiska tekniker och matematiska analyser som separerar
              vinnarna från förlorarna.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredPosts.length > 0 ? (
              featuredPosts.map((post, index) => (
                <div
                  key={post.slug}
                  className={`poker-card rounded-xl overflow-hidden group ${index === 0 ? "md:col-span-2 lg:col-span-1" : ""}`}
                >
                  <div className="relative overflow-hidden">
                    <Image
                      src={post.image || "/placeholder.svg?height=300&width=500"}
                      alt={post.title}
                      width={500}
                      height={300}
                      className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-slate-900/80 via-transparent to-transparent"></div>
                    <div className="absolute top-4 left-4">
                      <span className="px-3 py-1 bg-amber-500 text-slate-900 text-xs font-semibold rounded-full">
                        {post.category}
                      </span>
                    </div>
                    <div className="absolute bottom-4 left-4 right-4">
                      <div className="flex items-center gap-4 text-xs text-slate-300">
                        <span className="flex items-center gap-1">
                          <CalendarDays className="h-3 w-3" />
                          {new Date(post.date).toLocaleDateString("sv-SE")}
                        </span>
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {post.readTime}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="p-6">
                    <h3 className="font-crimson text-xl font-bold text-white mb-3 group-hover:text-amber-400 transition-colors line-clamp-2">
                      <Link href={`/artiklar/${post.slug}`}>{post.title}</Link>
                    </h3>
                    <p className="text-slate-400 mb-4 line-clamp-3 leading-relaxed">{post.excerpt}</p>
                    <Link
                      href={`/artiklar/${post.slug}`}
                      className="inline-flex items-center text-amber-400 hover:text-amber-300 font-medium group"
                    >
                      Läs fullständig analys
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-1 md:col-span-2 lg:col-span-3 text-center py-16">
                <div className="bg-slate-800/50 rounded-xl p-8 max-w-lg mx-auto">
                  <h3 className="text-xl font-semibold text-white mb-4">Inga artiklar hittades</h3>
                  <p className="text-slate-400 mb-6">
                    Det finns inga artiklar publicerade ännu. Kom tillbaka senare för spännande innehåll!
                  </p>
                </div>
              </div>
            )}
          </div>

          <div className="text-center mt-16">
            <Link
              href="/artiklar"
              className="inline-flex items-center px-8 py-4 border-2 border-amber-500/50 text-amber-400 font-semibold rounded-lg hover:bg-amber-500/10 transition-all duration-300 group"
            >
              Utforska Alla Artiklar
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-900 via-emerald-800 to-amber-900"></div>
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 text-4xl suit-spade"></div>
          <div className="absolute bottom-10 right-10 text-4xl suit-heart"></div>
        </div>

        <div className="relative container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="font-crimson text-4xl lg:text-5xl font-bold mb-6 text-white">
              Bli en <span className="text-amber-400">Pokermästare</span>
            </h2>
            <p className="text-emerald-100 text-lg mb-8 leading-relaxed">
              Få exklusiva strategier, avancerade tekniker och insider-tips direkt i din inkorg. Gå med i elitgruppen av
              seriösa pokerspelare.
            </p>
            <div className="max-w-md mx-auto">
              <div className="flex gap-3">
                <input
                  type="email"
                  placeholder="Din e-postadress"
                  className="flex-1 px-6 py-4 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-amber-400 focus:border-transparent"
                />
                <button className="px-6 py-4 bg-gradient-to-r from-amber-500 to-amber-600 text-slate-900 font-semibold rounded-lg hover:scale-105 transition-transform">
                  Gå Med
                </button>
              </div>
              <p className="text-emerald-200/80 text-sm mt-3">Ingen spam. Avregistrera dig när som helst.</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
