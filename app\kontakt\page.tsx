import type { Metada<PERSON> } from "next"
import { Mail, MessageSquare, Handshake } from "lucide-react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export const metadata: Metadata = {
  title: "Kontakt Pappa Haj - Poker Coach & Strategi Expert",
  description:
    "Kontakta Pappa Haj för pokerfr<PERSON>, coaching, samarbeten eller feedback. Professionell poker coach med svar inom 24 timmar. Specialiserad på NL Hold'em strategi, MTT coaching och bankroll management. Erbjuder personlig coaching och gruppträning.",
  keywords: [
    "kontakt pappa haj",
    "poker coach kontakt",
    "poker samarbete sverige",
    "poker coaching stockholm",
    "nl holdem coach",
    "mtt coaching",
    "poker strategi konsultation",
    "poker mentor sverige",
    "bankroll management coach",
    "poker expert kontakt"
  ],
  openGraph: {
    title: "Kontakt Pappa Haj - Poker Coach & Strategi Expert | Pappa Haj",
    description: "Kontakta Pappa Haj för poker<PERSON><PERSON><PERSON><PERSON>, coaching, samarbeten eller feedback. Professionell poker coach med svar inom 24 timmar.",
    url: "https://pappahaj.se/kontakt",
    type: "website",
  },
  twitter: {
    card: "summary",
    title: "Kontakt Pappa Haj - Poker Coach & Strategi Expert",
    description: "Kontakta Pappa Haj för pokerfrågor, coaching, samarbeten eller feedback. Svar inom 24 timmar.",
  },
}

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-slate-950 py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="font-playfair text-4xl lg:text-5xl font-bold mb-4 text-white">Kontakta Mig</h1>
          <p className="text-slate-400 text-lg max-w-2xl mx-auto">
            Har du frågor om poker, vill diskutera strategier eller är intresserad av samarbeten? Jag svarar gärna på
            alla meddelanden inom 24 timmar.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Contact Information */}
          <div className="space-y-6">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center text-white">
                  <Mail className="h-5 w-5 mr-2 text-emerald-400" />
                  E-post
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-400 mb-3">För allmänna frågor, feedback eller pokerdiskussioner:</p>
                <a
                  href="mailto:<EMAIL>"
                  className="text-emerald-400 hover:text-emerald-300 font-medium text-lg"
                >
                  <EMAIL>
                </a>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center text-white">
                  <Handshake className="h-5 w-5 mr-2 text-emerald-400" />
                  Samarbeten
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-400 mb-4">Jag är öppen för olika typer av samarbeten inom pokervärlden:</p>
                <ul className="space-y-2 text-slate-300">
                  <li>• Sponsorskap och partnerskap</li>
                  <li>• Gästinlägg och artiklar</li>
                  <li>• Produktrecensioner</li>
                  <li>• Pokercoaching och konsultation</li>
                  <li>• Event och turneringssamarbeten</li>
                </ul>
                <p className="text-slate-400 mt-4">Kontakta mig på samma e-postadress för att diskutera möjligheter.</p>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center text-white">
                  <MessageSquare className="h-5 w-5 mr-2 text-emerald-400" />
                  Vad du kan förvänta dig
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 text-slate-300">
                  <li className="flex items-start">
                    <span className="text-emerald-400 mr-2">✓</span>
                    Svar inom 24 timmar på vardagar
                  </li>
                  <li className="flex items-start">
                    <span className="text-emerald-400 mr-2">✓</span>
                    Personliga och genomtänkta svar
                  </li>
                  <li className="flex items-start">
                    <span className="text-emerald-400 mr-2">✓</span>
                    Professionell och vänlig kommunikation
                  </li>
                  <li className="flex items-start">
                    <span className="text-emerald-400 mr-2">✓</span>
                    Diskret hantering av känslig information
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Additional Information */}
          <div className="space-y-6">
            <Card className="bg-gradient-to-br from-emerald-900/50 to-slate-800 border-emerald-700/50">
              <CardHeader>
                <CardTitle className="text-white">Vanliga Frågor</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-semibold text-emerald-400 mb-2">Erbjuder du personlig pokercoaching?</h3>
                  <p className="text-slate-300 text-sm">
                    Ja, jag erbjuder både enskild coaching och gruppträning. Kontakta mig för mer information om priser
                    och tillgänglighet.
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold text-emerald-400 mb-2">Kan jag föreslå ämnen för framtida artiklar?</h3>
                  <p className="text-slate-300 text-sm">
                    Absolut! Jag uppskattar förslag från läsare och försöker täcka ämnen som intresserar communityn.
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold text-emerald-400 mb-2">Svarar du på pokerstrategi-frågor via e-post?</h3>
                  <p className="text-slate-300 text-sm">
                    Ja, jag svarar gärna på specifika strategifrågor, även om mer djupgående diskussioner kan kräva
                    coaching-sessioner.
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Responstider</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-400">Allmänna frågor:</span>
                    <span className="text-emerald-400 font-medium">24 timmar</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-400">Samarbetsförfrågningar:</span>
                    <span className="text-emerald-400 font-medium">48 timmar</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-400">Coaching-förfrågningar:</span>
                    <span className="text-emerald-400 font-medium">24 timmar</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-400">Helger:</span>
                    <span className="text-slate-400">Längre responstid</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="bg-slate-800 border border-slate-700 rounded-lg p-6">
              <h3 className="font-semibold text-white mb-3">Viktigt att veta</h3>
              <ul className="space-y-2 text-sm text-slate-400">
                <li>• Jag svarar inte på frågor om specifika pokersidor eller bonusar</li>
                <li>• Alla samarbeten utvärderas individuellt</li>
                <li>• Jag förbehåller mig rätten att inte svara på olämpliga meddelanden</li>
                <li>• För brådskande ärenden, ange detta tydligt i ämnesraden</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
