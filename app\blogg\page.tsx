import type { <PERSON>ada<PERSON> } from "next"
import Image from "next/image"
import Link from "next/link"
import { CalendarD<PERSON>, Clock, ArrowRight } from "lucide-react"
import { getAllPosts, getAllCategories } from "@/lib/blog"

export const metadata: Metadata = {
  title: "Artiklar - Alla Inlägg",
  description:
    "Läs alla artiklar från Pappa Haj om pokerstrategi, turneringar, bankroll management och mycket mer. Expertråd för att förbättra ditt pokerspel.",
  keywords: ["poker blogg", "pokerartiklar", "pokerstrategi artiklar", "poker tips blogg", "pappa haj blogg"],
  openGraph: {
    title: "Artiklar - Alla Inlägg | Pappa Haj",
    description: "Läs alla artiklar från Pappa Haj om pokerstrategi och expertråd.",
    url: "https://pappahaj.se/blogg",
  },
}

export default async function BlogPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  // Get the category from the URL query parameters
  const categoryParam = searchParams.kategori as string | undefined

  // Get all posts and categories
  const allPosts = await getAllPosts()
  const allCategories = await getAllCategories()

  // Filter posts by category if specified
  const posts = categoryParam ? allPosts.filter((post) => post.category === categoryParam) : allPosts

  return (
    <div className="min-h-screen bg-slate-950 py-12 pt-32">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="font-crimson text-4xl lg:text-5xl font-bold mb-4 text-white">Alla Artiklar</h1>
          <p className="text-slate-400 text-lg max-w-2xl mx-auto">
            Utforska alla artiklar om pokerstrategi, tips och expertråd för att förbättra ditt spel.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-2 mb-12">
          <Link
            href="/blogg"
            className={`px-4 py-2 rounded-full transition-all duration-300 ${
              !categoryParam
                ? "bg-amber-500 text-slate-900 font-medium"
                : "bg-slate-800 text-slate-300 hover:bg-amber-500/10 hover:text-amber-400"
            }`}
          >
            Alla
          </Link>

          {allCategories.map((category) => (
            <Link
              key={category}
              href={`/blogg?kategori=${encodeURIComponent(category)}`}
              className={`px-4 py-2 rounded-full transition-all duration-300 ${
                categoryParam === category
                  ? "bg-amber-500 text-slate-900 font-medium"
                  : "bg-slate-800 text-slate-300 hover:bg-amber-500/10 hover:text-amber-400"
              }`}
            >
              {category}
            </Link>
          ))}
        </div>

        {/* Blog Posts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {posts.length > 0 ? (
            posts.map((post) => (
              <div key={post.slug} className="poker-card rounded-xl overflow-hidden group">
                <div className="relative overflow-hidden">
                  <Image
                    src={post.image || "/placeholder.svg?height=300&width=500"}
                    alt={post.title}
                    width={500}
                    height={300}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-slate-900/80 via-transparent to-transparent"></div>
                  <div className="absolute top-4 left-4">
                    <span className="px-3 py-1 bg-amber-500 text-slate-900 text-xs font-semibold rounded-full">
                      {post.category}
                    </span>
                  </div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex items-center gap-4 text-xs text-slate-300">
                      <span className="flex items-center gap-1">
                        <CalendarDays className="h-3 w-3" />
                        {new Date(post.date).toLocaleDateString("sv-SE")}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {post.readTime}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="font-crimson text-xl font-bold text-white mb-3 group-hover:text-amber-400 transition-colors line-clamp-2">
                    <Link href={`/blogg/${post.slug}`}>{post.title}</Link>
                  </h3>
                  <p className="text-slate-400 mb-4 line-clamp-3 leading-relaxed">{post.excerpt}</p>
                  <Link
                    href={`/blogg/${post.slug}`}
                    className="inline-flex items-center text-amber-400 hover:text-amber-300 font-medium group"
                  >
                    Läs fullständig artikel
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-1 md:col-span-2 lg:col-span-3 text-center py-16">
              <div className="bg-slate-800/50 rounded-xl p-8 max-w-lg mx-auto">
                <h3 className="text-xl font-semibold text-white mb-4">Inga artiklar hittades</h3>
                <p className="text-slate-400 mb-6">
                  {categoryParam
                    ? `Det finns inga artiklar i kategorin "${categoryParam}" ännu.`
                    : "Det finns inga artiklar publicerade ännu."}
                </p>
                <Link
                  href="/blogg"
                  className="inline-flex items-center px-4 py-2 bg-amber-500 text-slate-900 rounded-lg font-medium"
                >
                  Visa alla artiklar
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
