"use client"

import { notFound } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { CalendarD<PERSON>, Clock, ArrowLef<PERSON>, Share2, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import type { BlogPost } from "@/lib/blog"
import { ReactNode } from "react"

interface BlogPostClientProps {
  params: { slug: string }
  post: BlogPost
  relatedPosts: BlogPost[]
  renderedContent: ReactNode
}

export default function BlogPostClient({ params, post, relatedPosts, renderedContent }: BlogPostClientProps) {
  if (!post) {
    notFound()
  }

  return (
    <article className="min-h-screen bg-slate-950 py-12 pt-32">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Back Button */}
        <Link href="/blogg" className="inline-flex items-center text-amber-400 hover:text-amber-300 mb-8 group">
          <ArrowLeft className="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform" />
          Tillba<PERSON> till artiklar
        </Link>

        {/* Article Header */}
        <header className="mb-8">
          <div className="mb-4">
            <Link
              href={`/blogg?kategori=${encodeURIComponent(post.category)}`}
              className="inline-block px-3 py-1 bg-amber-500 hover:bg-amber-600 text-slate-900 text-sm font-semibold rounded-full transition-colors"
            >
              {post.category}
            </Link>
          </div>

          <h1 className="font-crimson text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight">{post.title}</h1>

          <p className="text-xl text-slate-400 mb-6 leading-relaxed">{post.excerpt}</p>

          <div className="flex items-center justify-between flex-wrap gap-4">
            <div className="flex items-center gap-6 text-slate-500">
              <span className="flex items-center gap-2">
                <User className="h-4 w-4" />
                {post.author}
              </span>
              <span className="flex items-center gap-2">
                <CalendarDays className="h-4 w-4" />
                {new Date(post.date).toLocaleDateString("sv-SE", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </span>
              <span className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                {post.readTime}
              </span>
            </div>

            <Button
              variant="outline"
              size="sm"
              className="border-slate-600 text-slate-300 hover:bg-slate-800"
              onClick={() => {
                if (navigator.share) {
                  navigator.share({
                    title: post.title,
                    text: post.excerpt,
                    url: window.location.href,
                  })
                } else {
                  navigator.clipboard.writeText(window.location.href)
                  alert("Länk kopierad till urklipp!")
                }
              }}
            >
              <Share2 className="h-4 w-4 mr-2" />
              Dela artikel
            </Button>
          </div>
        </header>

        {/* Featured Image */}
        <div className="mb-8 rounded-xl overflow-hidden">
          <Image
            src={post.image || "/placeholder.svg?height=400&width=800"}
            alt={post.title}
            width={800}
            height={400}
            className="w-full h-64 lg:h-96 object-cover"
            priority
          />
        </div>

        {/* Article Content */}
        <div className="prose prose-slate prose-invert max-w-none">
          {renderedContent}
        </div>

        {/* Article Footer */}
        <footer className="mt-12 pt-8 border-t border-slate-800">
          <div className="flex items-center justify-between">
            <Link href="/blogg" className="inline-flex items-center text-amber-400 hover:text-amber-300 group">
              <ArrowLeft className="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform" />
              Fler artiklar
            </Link>

            <Button
              variant="outline"
              className="border-amber-400 text-amber-400 hover:bg-amber-400 hover:text-slate-900"
              onClick={() => {
                if (navigator.share) {
                  navigator.share({
                    title: post.title,
                    text: post.excerpt,
                    url: window.location.href,
                  })
                } else {
                  navigator.clipboard.writeText(window.location.href)
                  alert("Länk kopierad till urklipp!")
                }
              }}
            >
              <Share2 className="h-4 w-4 mr-2" />
              Dela artikel
            </Button>
          </div>
        </footer>

        {/* Related Articles */}
        {relatedPosts.length > 0 && (
          <div className="mt-16">
            <h2 className="font-crimson text-2xl font-bold text-white mb-6">Relaterade artiklar</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {relatedPosts.map((relatedPost) => (
                <Link
                  key={relatedPost.slug}
                  href={`/blogg/${relatedPost.slug}`}
                  className="poker-card rounded-lg overflow-hidden group"
                >
                  <div className="relative h-40">
                    <Image
                      src={relatedPost.image || "/placeholder.svg?height=200&width=300"}
                      alt={relatedPost.title}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-slate-900/80 via-transparent to-transparent"></div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-crimson text-lg font-bold text-white group-hover:text-amber-400 transition-colors line-clamp-2">
                      {relatedPost.title}
                    </h3>
                    <div className="flex items-center gap-4 mt-2 text-xs text-slate-500">
                      <span className="flex items-center gap-1">
                        <CalendarDays className="h-3 w-3" />
                        {new Date(relatedPost.date).toLocaleDateString("sv-SE")}
                      </span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </article>
  )
}
