import { BlogPost } from "@/lib/blog"

interface WebsiteSchemaProps {
  url: string
  name: string
  description: string
}

interface ArticleSchemaProps {
  post: BlogPost
  url: string
}

interface PersonSchemaProps {
  name: string
  description: string
  url: string
  image?: string
}

interface BreadcrumbSchemaProps {
  items: Array<{
    name: string
    url: string
  }>
}

export function WebsiteSchema({ url, name, description }: WebsiteSchemaProps) {
  const schema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name,
    description,
    url,
    potentialAction: {
      "@type": "SearchAction",
      target: {
        "@type": "EntryPoint",
        urlTemplate: `${url}/artiklar?search={search_term_string}`,
      },
      "query-input": "required name=search_term_string",
    },
    publisher: {
      "@type": "Person",
      name: "Pappa Haj",
      description: "Pokerstrategi expert och coach",
      url: `${url}/om`,
    },
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  )
}

export function ArticleSchema({ post, url }: ArticleSchemaProps) {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Article",
    headline: post.title,
    description: post.excerpt,
    image: post.image ? `https://pappahaj.se${post.image}` : undefined,
    datePublished: post.date,
    dateModified: post.date,
    author: {
      "@type": "Person",
      name: "Pappa Haj",
      description: "Pokerstrategi expert och coach med över 10 års erfarenhet",
      url: "https://pappahaj.se/om",
    },
    publisher: {
      "@type": "Organization",
      name: "Pappa Haj",
      description: "Sveriges ledande pokerblogg",
      url: "https://pappahaj.se",
      logo: {
        "@type": "ImageObject",
        url: "https://pappahaj.se/images/pappa-haj-logo.jpg",
        width: 80,
        height: 80,
      },
    },
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": url,
    },
    articleSection: post.category,
    keywords: [
      "poker",
      "pokerstrategi",
      post.category.toLowerCase(),
      "nl holdem",
      "mtt",
      "poker tips",
      "poker coaching",
    ],
    about: {
      "@type": "Thing",
      name: "Pokerstrategi",
      description: "Strategier och tekniker för att förbättra pokerspel",
    },
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  )
}

export function PersonSchema({ name, description, url, image }: PersonSchemaProps) {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Person",
    name,
    description,
    url,
    image: image ? `https://pappahaj.se${image}` : undefined,
    jobTitle: "Pokerstrategi Expert & Coach",
    worksFor: {
      "@type": "Organization",
      name: "Pappa Haj",
      url: "https://pappahaj.se",
    },
    knowsAbout: [
      "Poker Strategy",
      "No Limit Hold'em",
      "Multi-Table Tournaments",
      "Bankroll Management",
      "Poker Psychology",
      "Game Theory",
    ],
    sameAs: [
      "https://pappahaj.se",
    ],
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  )
}

export function BreadcrumbSchema({ items }: BreadcrumbSchemaProps) {
  const schema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: items.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  )
}

export function BlogSchema() {
  const schema = {
    "@context": "https://schema.org",
    "@type": "Blog",
    name: "Pappa Haj Pokerartiklar",
    description: "Sveriges ledande pokersajt med expertstrategier och analyser",
    url: "https://pappahaj.se/artiklar",
    author: {
      "@type": "Person",
      name: "Pappa Haj",
      description: "Pokerstrategi expert och coach",
      url: "https://pappahaj.se/om",
    },
    publisher: {
      "@type": "Organization",
      name: "Pappa Haj",
      url: "https://pappahaj.se",
      logo: {
        "@type": "ImageObject",
        url: "https://pappahaj.se/images/pappa-haj-logo.jpg",
        width: 80,
        height: 80,
      },
    },
    inLanguage: "sv-SE",
    about: {
      "@type": "Thing",
      name: "Pokerstrategi",
      description: "Strategier, tips och tekniker för att förbättra pokerspel",
    },
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  )
}
