import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter, Crimson_Text, Playfair_Display } from "next/font/google"
import "./globals.css"
import Header from "@/components/header"
import Footer from "@/components/footer"
import { WebsiteSchema } from "@/components/structured-data"
import CookieBanner from "@/components/cookie-banner"

const inter = Inter({ subsets: ["latin"], variable: "--font-inter" })
const crimson = Crimson_Text({
  subsets: ["latin"],
  variable: "--font-crimson",
  weight: ["400", "600", "700"],
})
const playfair = Playfair_Display({ subsets: ["latin"], variable: "--font-playfair" })

export const metadata: Metadata = {
  title: {
    default: "Pappa Haj - Pokerblogg & Strategier",
    template: "%s | Pappa Haj",
  },
  description:
    "Pappa Haj är din guide till pokervärlden. Läs om strategier, turneringar, och få expertr<PERSON>d för att förbättra ditt pokerspel.",
  keywords: ["poker", "pokerstrategi", "pokerturneringar", "pokertips", "texas holdem", "pappa haj"],
  authors: [{ name: "Pappa Haj" }],
  creator: "Pappa Haj",
  publisher: "Pappa Haj",
  metadataBase: new URL("https://pappahaj.se"),
  alternates: {
    canonical: "/",
  },
  other: {
    "sitemap": "/sitemap.xml",
  },
  openGraph: {
    type: "website",
    locale: "sv_SE",
    url: "https://pappahaj.se",
    title: "Pappa Haj - Pokerblogg & Strategier",
    description: "Din guide till pokervärlden med strategier, tips och expertråd.",
    siteName: "Pappa Haj",
  },
  twitter: {
    card: "summary_large_image",
    title: "Pappa Haj - Pokerblogg & Strategier",
    description: "Din guide till pokervärlden med strategier, tips och expertråd.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="sv" className={`${inter.variable} ${crimson.variable} ${playfair.variable}`}>
      <head>
        <WebsiteSchema
          url="https://pappahaj.se"
          name="Pappa Haj - Pokerstrategi Expert"
          description="Sveriges ledande pokerblogg med expertstrategier, djupgående analyser och beprövade tekniker från en erfaren poker coach."
        />
      </head>
      <body className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-emerald-950 text-white">
        <Header />
        <main className="flex-1">{children}</main>
        <Footer />
        <CookieBanner />
      </body>
    </html>
  )
}
