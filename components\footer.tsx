"use client"

import Link from "next/link"
import { Spade, Mail, MapPin, ExternalLink } from "lucide-react"

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-gradient-to-b from-slate-950 via-slate-950 to-black border-t border-amber-500/20 relative overflow-hidden">
      {/* Decorative background elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 text-4xl suit-spade"></div>
        <div className="absolute top-20 right-20 text-4xl suit-heart"></div>
        <div className="absolute bottom-20 left-20 text-4xl suit-diamond"></div>
        <div className="absolute bottom-10 right-10 text-4xl suit-club"></div>
      </div>

      <div className="relative container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
          {/* Brand & Contact */}
          <div className="lg:col-span-2">
            <Link href="/" className="flex items-center space-x-3 mb-6 group">
              <div className="relative">
                <div className="bg-gradient-to-br from-amber-400 to-amber-600 p-3 rounded-xl shadow-xl group-hover:scale-105 transition-transform">
                  <Spade className="h-7 w-7 text-slate-900" />
                </div>
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-emerald-400 rounded-full animate-pulse"></div>
              </div>
              <div>
                <span className="font-crimson text-3xl font-bold bg-gradient-to-r from-amber-400 to-amber-600 bg-clip-text text-transparent">
                  Pappa Haj
                </span>
                <div className="text-sm text-amber-400/80 font-medium tracking-wider">Poker Expert</div>
              </div>
            </Link>

            <p className="text-slate-300 mb-8 max-w-md leading-relaxed text-lg">
              Mästra pokerkonsten med expertstrategier och beprövade tekniker. Din guide till professionell pokersuccess
              sedan 2014.
            </p>

            {/* Contact Information */}
            <div className="space-y-4">
              <div className="flex items-center space-x-4 text-slate-300 group">
                <div className="p-3 bg-slate-800/50 rounded-lg group-hover:bg-amber-500/10 transition-colors">
                  <Mail className="h-5 w-5 text-amber-400" />
                </div>
                <div>
                  <div className="text-sm text-slate-500">E-post</div>
                  <a
                    href="mailto:<EMAIL>"
                    className="hover:text-amber-400 transition-colors font-medium text-lg"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>

              <div className="flex items-center space-x-4 text-slate-300">
                <div className="p-3 bg-slate-800/50 rounded-lg">
                  <MapPin className="h-5 w-5 text-amber-400" />
                </div>
                <div>
                  <div className="text-sm text-slate-500">Baserad i</div>
                  <span className="font-medium">Sverige</span>
                </div>
              </div>
            </div>

            {/* Newsletter Signup */}
            <div className="mt-8 p-6 bg-gradient-to-r from-slate-800/50 to-emerald-900/20 rounded-xl border border-slate-700/50">
              <h4 className="font-crimson text-lg font-semibold text-white mb-3">Få de senaste artiklarna</h4>
              <div className="flex gap-2">
                <input
                  type="email"
                  placeholder="Din e-postadress"
                  className="flex-1 px-4 py-2 bg-slate-800 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-amber-400 focus:border-transparent text-sm"
                />
                <button className="px-4 py-2 bg-gradient-to-r from-amber-500 to-amber-600 text-slate-900 font-semibold rounded-lg hover:scale-105 transition-transform text-sm">
                  Prenumerera
                </button>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div>
            <h3 className="font-crimson text-xl font-semibold text-white mb-6 flex items-center">
              <span className="w-1 h-6 bg-amber-400 rounded-full mr-3"></span>
              Navigation
            </h3>
            <ul className="space-y-4">
              <li>
                <Link
                  href="/"
                  className="text-slate-400 hover:text-amber-400 transition-all duration-300 flex items-center group text-lg"
                >
                  <span className="w-2 h-2 bg-amber-400 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Hem
                </Link>
              </li>
              <li>
                <Link
                  href="/artiklar"
                  className="text-slate-400 hover:text-amber-400 transition-all duration-300 flex items-center group text-lg"
                >
                  <span className="w-2 h-2 bg-amber-400 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Artiklar
                </Link>
              </li>
              <li>
                <Link
                  href="/om"
                  className="text-slate-400 hover:text-amber-400 transition-all duration-300 flex items-center group text-lg"
                >
                  <span className="w-2 h-2 bg-amber-400 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Om Mig
                </Link>
              </li>
              <li>
                <Link
                  href="/kontakt"
                  className="text-slate-400 hover:text-amber-400 transition-all duration-300 flex items-center group text-lg"
                >
                  <span className="w-2 h-2 bg-amber-400 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Kontakt
                </Link>
              </li>
            </ul>

            {/* Quick Links */}
            <div className="mt-8">
              <h4 className="font-semibold text-white mb-4">Populära Kategorier</h4>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="/artiklar?kategori=Strategi"
                    className="text-slate-500 hover:text-amber-400 transition-colors text-sm"
                  >
                    Pokerstrategi
                  </Link>
                </li>
                <li>
                  <Link
                    href="/artiklar?kategori=Psykologi"
                    className="text-slate-500 hover:text-amber-400 transition-colors text-sm"
                  >
                    Pokerpsykologi
                  </Link>
                </li>
                <li>
                  <Link
                    href="/artiklar?kategori=Ekonomi"
                    className="text-slate-500 hover:text-amber-400 transition-colors text-sm"
                  >
                    Bankroll Management
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          {/* Legal & Resources */}
          <div>
            <h3 className="font-crimson text-xl font-semibold text-white mb-6 flex items-center">
              <span className="w-1 h-6 bg-amber-400 rounded-full mr-3"></span>
              Juridiskt & Info
            </h3>
            <ul className="space-y-4">
              <li>
                <Link
                  href="/integritetspolicy"
                  className="text-slate-400 hover:text-amber-400 transition-all duration-300 flex items-center group text-lg"
                >
                  <span className="w-2 h-2 bg-amber-400 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Integritetspolicy
                </Link>
              </li>
              <li>
                <Link
                  href="/cookie-policy"
                  className="text-slate-400 hover:text-amber-400 transition-all duration-300 flex items-center group text-lg"
                >
                  <span className="w-2 h-2 bg-amber-400 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Cookie Policy
                </Link>
              </li>
              <li>
                <Link
                  href="/ansvarsfriskrivning"
                  className="text-slate-400 hover:text-amber-400 transition-all duration-300 flex items-center group text-lg"
                >
                  <span className="w-2 h-2 bg-amber-400 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Ansvarsfriskrivning
                </Link>
              </li>
              <li>
                <Link
                  href="/sitemap"
                  className="text-slate-400 hover:text-amber-400 transition-all duration-300 flex items-center group text-lg"
                >
                  <span className="w-2 h-2 bg-amber-400 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Sitemap
                </Link>
              </li>
            </ul>

            {/* External Resources */}
            <div className="mt-8">
              <h4 className="font-semibold text-white mb-4">Resurser</h4>
              <ul className="space-y-2">
                <li>
                  <a
                    href="https://www.pokernews.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-slate-500 hover:text-amber-400 transition-colors text-sm flex items-center group"
                  >
                    PokerNews
                    <ExternalLink className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </a>
                </li>
                <li>
                  <a
                    href="https://www.twoplustwo.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-slate-500 hover:text-amber-400 transition-colors text-sm flex items-center group"
                  >
                    Two Plus Two
                    <ExternalLink className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </a>
                </li>
                <li>
                  <a
                    href="https://www.wsop.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-slate-500 hover:text-amber-400 transition-colors text-sm flex items-center group"
                  >
                    WSOP
                    <ExternalLink className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-slate-800/50 mt-16 pt-8">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-6">
            <div className="flex flex-col sm:flex-row items-center gap-6 text-slate-500 text-sm">
              <p>&copy; {currentYear} Pappa Haj. Alla rättigheter förbehållna.</p>
              <div className="flex items-center gap-4">
                <span className="flex items-center gap-1">
                  Gjord med <span className="text-red-500">♥</span> för pokerspelare
                </span>
                <span className="hidden sm:block">•</span>
                <span>Senast uppdaterad: {new Date().toLocaleDateString("sv-SE")}</span>
              </div>
            </div>

            {/* Back to top */}
            <button
              onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
              className="flex items-center gap-2 text-slate-400 hover:text-amber-400 transition-colors group"
            >
              <span className="text-sm">Tillbaka till toppen</span>
              <div className="p-2 bg-slate-800 rounded-lg group-hover:bg-amber-500/10 transition-colors">
                <svg
                  className="h-4 w-4 transform group-hover:-translate-y-1 transition-transform"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
              </div>
            </button>
          </div>
        </div>
      </div>
    </footer>
  )
}
