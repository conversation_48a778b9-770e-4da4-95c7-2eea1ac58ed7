import fs from "fs"
import path from "path"
import matter from "gray-matter"

// Define the content directory
const contentDirectory = path.join(process.cwd(), "content")
const imagesDirectory = "/images" // This will be used as a prefix for image URLs

// Define the BlogPost type
export type BlogPost = {
  slug: string
  title: string
  excerpt: string
  date: string
  readTime: string
  category: string
  author: string
  image: string
  content: string
  keywords: string[]
}

// Get all blog posts
export async function getAllPosts(): Promise<BlogPost[]> {
  // Create content directory if it doesn't exist
  if (!fs.existsSync(contentDirectory)) {
    fs.mkdirSync(contentDirectory, { recursive: true })
    // Maybe create a sample post for first-time setup
    const samplePost = `---
title: "Välkommen till Pappa Haj"
excerpt: "Detta är ett exempel på ett blogginlägg. Ersätt detta med ditt eget innehåll."
date: "${new Date().toISOString().split("T")[0]}"
readTime: "2 min"
category: "Allmänt"
image: "/images/welcome.jpg"
keywords: ["välkommen", "pappa haj", "poker"]
---

# Välkommen till Pappa Haj

Detta är ett exempel på ett blogginlägg. Du kan redigera eller ta bort detta och lägga till dina egna inlägg.

## Hur du lägger till nya inlägg

1. Skapa en ny markdown-fil i \`content\`-mappen
2. Lägg till frontmatter med titel, utdrag, datum, etc.
3. Skriv ditt innehåll i markdown-format
4. Lägg till en hero-bild i \`public/images\` med samma namn som din markdown-fil

Lycka till med din blogg!
`
    fs.writeFileSync(path.join(contentDirectory, "welcome.md"), samplePost)
  }

  // Get all files from the content directory
  const fileNames = fs.readdirSync(contentDirectory)

  // Get all blog posts
  const allPosts = fileNames
    .filter((fileName) => fileName.endsWith(".md"))
    .map((fileName) => {
      // Remove ".md" from file name to get slug
      const slug = fileName.replace(/\.md$/, "")

      // Read markdown file as string
      const fullPath = path.join(contentDirectory, fileName)
      const fileContents = fs.readFileSync(fullPath, "utf8")

      // Use gray-matter to parse the post metadata section
      const { data, content } = matter(fileContents)

      // Ensure image path is correct
      const image = data.image?.startsWith("/") ? data.image : `${imagesDirectory}/${slug}.jpg` // Default to slug-based image if not specified

      // Combine the data with the slug
      return {
        slug,
        title: data.title || "",
        excerpt: data.excerpt || "",
        date: data.date || new Date().toISOString().split("T")[0],
        readTime: data.readTime || "5 min",
        category: data.category || "Allmänt",
        author: data.author || "Pappa Haj",
        image,
        content,
        keywords: data.keywords || [],
      } as BlogPost
    })

  // Sort posts by date
  return allPosts.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
}

// Get a single blog post by slug
export async function getPostBySlug(slug: string): Promise<BlogPost | null> {
  try {
    const fullPath = path.join(contentDirectory, `${slug}.md`)

    // Check if the file exists
    if (!fs.existsSync(fullPath)) {
      return null
    }

    const fileContents = fs.readFileSync(fullPath, "utf8")
    const { data, content } = matter(fileContents)

    // Ensure image path is correct
    const image = data.image?.startsWith("/") ? data.image : `${imagesDirectory}/${slug}.jpg` // Default to slug-based image if not specified

    return {
      slug,
      title: data.title || "",
      excerpt: data.excerpt || "",
      date: data.date || "",
      readTime: data.readTime || "5 min",
      category: data.category || "Allmänt",
      author: data.author || "Pappa Haj",
      image,
      content,
      keywords: data.keywords || [],
    }
  } catch (error) {
    console.error(`Error getting post by slug: ${slug}`, error)
    return null
  }
}

// Get all categories from blog posts
export async function getAllCategories(): Promise<string[]> {
  const posts = await getAllPosts()
  const categories = posts.map((post) => post.category)

  // Remove duplicates and sort alphabetically
  return Array.from(new Set(categories)).sort()
}

// Get posts by category
export async function getPostsByCategory(category: string): Promise<BlogPost[]> {
  const allPosts = await getAllPosts()

  if (category === "Alla") {
    return allPosts
  }

  return allPosts.filter((post) => post.category === category)
}

// Get all URLs for sitemap generation
export async function getAllSitemapUrls(): Promise<Array<{
  url: string
  lastModified: Date
  changeFrequency: string
  priority: number
}>> {
  const baseUrl = "https://pappahaj.se"
  const posts = await getAllPosts()

  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: "daily",
      priority: 1.0,
    },
    {
      url: `${baseUrl}/artiklar`,
      lastModified: new Date(),
      changeFrequency: "daily",
      priority: 0.9,
    },
    {
      url: `${baseUrl}/blogg`,
      lastModified: new Date(),
      changeFrequency: "daily",
      priority: 0.9,
    },
    {
      url: `${baseUrl}/om`,
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.7,
    },
    {
      url: `${baseUrl}/kontakt`,
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.6,
    },
    {
      url: `${baseUrl}/integritetspolicy`,
      lastModified: new Date(),
      changeFrequency: "yearly",
      priority: 0.3,
    },
    {
      url: `${baseUrl}/cookie-policy`,
      lastModified: new Date(),
      changeFrequency: "yearly",
      priority: 0.3,
    },
    {
      url: `${baseUrl}/ansvarsfriskrivning`,
      lastModified: new Date(),
      changeFrequency: "yearly",
      priority: 0.3,
    },
  ]

  // Dynamic blog post pages
  const blogPages = posts.map((post) => ({
    url: `${baseUrl}/artiklar/${post.slug}`,
    lastModified: new Date(post.date),
    changeFrequency: "weekly",
    priority: 0.8,
  }))

  // Also include blogg URLs for backward compatibility
  const bloggPages = posts.map((post) => ({
    url: `${baseUrl}/blogg/${post.slug}`,
    lastModified: new Date(post.date),
    changeFrequency: "weekly",
    priority: 0.8,
  }))

  return [...staticPages, ...blogPages, ...bloggPages]
}
