"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "lucide-react"
import Link from "next/link"

export default function CookieBanner() {
  const [showBanner, setShowBanner] = useState(false)

  useEffect(() => {
    const consent = localStorage.getItem("cookie-consent")
    if (!consent) {
      setShowBanner(true)
    }
  }, [])

  const acceptCookies = () => {
    localStorage.setItem("cookie-consent", "accepted")
    setShowBanner(false)
  }

  const declineCookies = () => {
    localStorage.setItem("cookie-consent", "declined")
    setShowBanner(false)
  }

  if (!showBanner) return null

  return (
    <div className="fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:max-w-md">
      <Card className="bg-slate-900 border-slate-700 p-6">
        <div className="flex items-start space-x-3">
          <Cookie className="h-6 w-6 text-emerald-400 flex-shrink-0 mt-1" />
          <div className="flex-1">
            <h3 className="font-semibold text-white mb-2">Vi använder cookies</h3>
            <p className="text-sm text-slate-400 mb-4">
              Vi använder cookies för att förbättra din upplevelse på vår webbplats. Genom att fortsätta använda sidan
              godkänner du vår användning av cookies.
            </p>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={acceptCookies} size="sm" className="bg-emerald-600 hover:bg-emerald-700 text-white">
                Acceptera
              </Button>
              <Button
                onClick={declineCookies}
                size="sm"
                variant="outline"
                className="border-slate-600 text-slate-300 hover:bg-slate-800"
              >
                Avböj
              </Button>
              <Link href="/cookie-policy" className="text-xs text-emerald-400 hover:text-emerald-300 self-center">
                Läs mer
              </Link>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}
