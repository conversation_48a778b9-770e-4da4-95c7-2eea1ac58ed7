"use client"

import { notFound } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { CalendarD<PERSON>, Clock, ArrowLeft, <PERSON>hare2, <PERSON><PERSON><PERSON>, Eye, User } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import type { BlogPost } from "@/lib/blog"
import { ReactNode } from "react"



interface ArticleClientProps {
  params: { slug: string }
  post: BlogPost
  relatedPosts: BlogPost[]
  renderedContent: ReactNode
}

export default function ArticleClient({ params, post, relatedPosts, renderedContent }: ArticleClientProps) {
  if (!post) {
    notFound()
  }

  return (
    <article className="min-h-screen bg-slate-950 py-12 pt-32">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Back Button */}
        <Link href="/artiklar" className="inline-flex items-center text-amber-400 hover:text-amber-300 mb-8 group">
          <ArrowLeft className="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform" />
          <PERSON>ba<PERSON> till artiklar
        </Link>

        {/* Article Header */}
        <header className="mb-8">
          <div className="flex items-center gap-4 mb-6">
            <Link
              href={`/artiklar?kategori=${encodeURIComponent(post.category)}`}
              className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-slate-900 text-sm font-semibold rounded-full transition-all duration-300"
            >
              {post.category}
            </Link>
            <div className="flex items-center gap-2 text-slate-500 text-sm">
              <BookOpen className="h-4 w-4" />
              <span>Artikel</span>
            </div>
          </div>

          <h1 className="font-crimson text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight">{post.title}</h1>

          <p className="text-xl text-slate-400 mb-8 leading-relaxed">{post.excerpt}</p>

          <div className="flex items-center justify-between flex-wrap gap-4 pb-8 border-b border-slate-800">
            <div className="flex items-center gap-6 text-slate-500">
              <span className="flex items-center gap-2">
                <User className="h-4 w-4" />
                {post.author}
              </span>
              <span className="flex items-center gap-2">
                <CalendarDays className="h-4 w-4" />
                {new Date(post.date).toLocaleDateString("sv-SE", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </span>
              <span className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                {post.readTime}
              </span>
              <span className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Expertanalys
              </span>
            </div>

            <Button
              variant="outline"
              size="sm"
              className="border-slate-600 text-slate-300 hover:bg-slate-800 hover:border-amber-500"
              onClick={() => {
                if (navigator.share) {
                  navigator.share({
                    title: post.title,
                    text: post.excerpt,
                    url: window.location.href,
                  })
                } else {
                  navigator.clipboard.writeText(window.location.href)
                  alert("Länk kopierad till urklipp!")
                }
              }}
            >
              <Share2 className="h-4 w-4 mr-2" />
              Dela artikel
            </Button>
          </div>
        </header>

        {/* Featured Image */}
        <div className="mb-12 rounded-xl overflow-hidden shadow-2xl">
          <Image
            src={post.image || "/placeholder.svg?height=400&width=800"}
            alt={post.title}
            width={800}
            height={400}
            className="w-full h-64 lg:h-96 object-cover"
            priority
          />
        </div>

        {/* Article Content */}
        <div className="max-w-none">
          <div className="bg-slate-900/30 border border-slate-800 rounded-xl p-8 mb-8">
            <h2 className="font-crimson text-xl font-bold text-white mb-4 flex items-center">
              <BookOpen className="h-5 w-5 text-amber-400 mr-2" />I denna artikel lär du dig:
            </h2>
            <ul className="list-disc list-inside space-y-2 text-slate-300">
              {post.keywords.slice(0, 4).map((keyword, index) => (
                <li key={index} className="capitalize">
                  {keyword.replace("-", " ")}
                </li>
              ))}
            </ul>
          </div>

          <div className="prose prose-slate prose-invert max-w-none">
            {renderedContent}
          </div>
        </div>

        {/* Article Footer */}
        <footer className="mt-16 pt-8 border-t border-slate-800">
          <div className="bg-slate-900/50 border border-slate-800 rounded-xl p-8 mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-crimson text-lg font-bold text-white mb-2">Var denna artikel hjälpsam?</h3>
                <p className="text-slate-400 text-sm">Dela den med andra pokerspelare som kan ha nytta av den.</p>
              </div>
              <Button
                className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-slate-900 font-semibold"
                onClick={() => {
                  if (navigator.share) {
                    navigator.share({
                      title: post.title,
                      text: post.excerpt,
                      url: window.location.href,
                    })
                  } else {
                    navigator.clipboard.writeText(window.location.href)
                    alert("Länk kopierad till urklipp!")
                  }
                }}
              >
                <Share2 className="h-4 w-4 mr-2" />
                Dela artikel
              </Button>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <Link href="/artiklar" className="inline-flex items-center text-amber-400 hover:text-amber-300 group">
              <ArrowLeft className="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform" />
              Fler artiklar
            </Link>

            <Link href="/kontakt" className="text-slate-400 hover:text-amber-400 text-sm transition-colors">
              Har du frågor? Kontakta mig
            </Link>
          </div>
        </footer>

        {/* Related Articles */}
        {relatedPosts.length > 0 && (
          <div className="mt-16">
            <div className="text-center mb-8">
              <h2 className="font-crimson text-3xl font-bold text-white mb-4">Relaterade artiklar</h2>
              <p className="text-slate-400">Fler artiklar inom {post.category}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {relatedPosts.map((relatedPost, index) => (
                <Link
                  key={relatedPost.slug}
                  href={`/artiklar/${relatedPost.slug}`}
                  className="poker-card rounded-xl overflow-hidden group"
                >
                  <div className="relative h-40">
                    <Image
                      src={relatedPost.image || "/placeholder.svg?height=200&width=300"}
                      alt={relatedPost.title}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-slate-900/90 via-transparent to-transparent"></div>
                    <div className="absolute top-3 left-3">
                      <span className="px-2 py-1 bg-amber-500 text-slate-900 text-xs font-semibold rounded-full">
                        {relatedPost.category}
                      </span>
                    </div>
                    <div className="absolute top-3 right-3">
                      <div className="w-6 h-6 bg-slate-900/80 backdrop-blur-sm rounded-full flex items-center justify-center">
                        <span className="text-xs font-bold text-amber-400">#{index + 1}</span>
                      </div>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-crimson text-lg font-bold text-white group-hover:text-amber-400 transition-colors line-clamp-2 mb-2">
                      {relatedPost.title}
                    </h3>
                    <div className="flex items-center gap-4 text-xs text-slate-500">
                      <span className="flex items-center gap-1">
                        <CalendarDays className="h-3 w-3" />
                        {new Date(relatedPost.date).toLocaleDateString("sv-SE")}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {relatedPost.readTime}
                      </span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </article>
  )
}
